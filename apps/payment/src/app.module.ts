import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 配置
import appConfig from './config/app.config';

// 功能模块
import { HealthModule } from './modules/health/health.module';
import { OrderModule } from "./modules/order/order.module";
import { GatewayModule } from "./modules/gateway/gateway.module";
import { CallbackModule } from "./modules/callback/callback.module";
// AuditModule已删除，审计服务迁移到common/services/audit

// 基础设施模块
import { createMongoConfig, setupDatabaseEvents } from '@libs/database/mongodb.config';
import { RedisModule } from '@libs/redis';
import { GameConfigModule } from '@libs/game-config';
import { ServiceMeshModule } from "@libs/service-mesh";
import { ScheduleModule } from "@nestjs/schedule";


@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      load: [appConfig],
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`,  // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/payment/.env.server',                    // 4. 区服配置
        'apps/payment/.env.redis',                     // 5. Redis业务配置
        'apps/payment/.env.database',                  // 6. 数据库配置
        'apps/payment/.env.security',                  // 7. 安全业务配置

        // 环境特定的业务配置（如果存在）
        `apps/payment/.env.server.${process.env.NODE_ENV || 'development'}`,
        `apps/payment/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/payment/.env.database.${process.env.NODE_ENV || 'development'}`,
        `apps/payment/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/payment/.env.local',                     // 8. 服务本地覆盖
      ],
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),

    // 数据库模块
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...createMongoConfig(configService, 'payment'),
        ...setupDatabaseEvents('payment'),
      }),
      inject: [ConfigService],
    }),

    // Redis模块
    RedisModule.forRootAsync({
      service: 'payment',
      serverId: process.env.SERVER_ID || 'server_001', // 从环境变量获取区服ID
    }),

    // 游戏配置模块（核心基础设施）
    GameConfigModule.forRootAsync(),

    // 🚀 统一ServiceMesh架构 - 一行配置替代复杂的双重配置
    // 自动推断：role=server, services=[character,hero], serverId=server_001
    ServiceMeshModule.register('payment'),

    // 功能模块
    HealthModule,
    OrderModule,      // 支付订单管理
    GatewayModule,    // 第三方支付网关集成
    CallbackModule,   // 支付回调处理
    // AuditModule已删除，审计服务迁移到common/services/audit
  ],
})
export class AppModule {}
