/**
 * Payment支付服务配置（精简版）
 *
 * 只包含实际使用的配置项：
 * - 服务基础配置
 * - 支付宝网关配置
 * - 订单管理配置
 * - 审计日志配置
 */

import { registerAs } from '@nestjs/config';

export interface PaymentGatewayConfig {
  /** 支付宝配置 */
  alipay: {
    appId: string;
    privateKey: string;
    publicKey: string;
    gatewayUrl: string;
    notifyUrl: string;
    returnUrl: string;
    signType: string;
    charset: string;
    version: string;
  };

  /** 微信支付配置 */
  wechat: {
    appId: string;
    mchId: string;
    apiKey: string;
    certPath: string;
    keyPath: string;
    notifyUrl: string;
    apiVersion: string;
  };

  /** 苹果支付配置 */
  apple: {
    bundleId: string;
    environment: 'sandbox' | 'production';
    sharedSecret: string;
    verifyReceiptUrl: string;
  };
}

export interface PaymentSecurityConfig {
  /** 签名密钥 */
  signSecret: string;
  /** 加密密钥 */
  encryptSecret: string;
  /** 签名算法 */
  signAlgorithm: string;
  /** 加密算法 */
  encryptAlgorithm: string;
  /** 回调IP白名单 */
  callbackWhitelist: string[];
  /** 订单超时时间（分钟） */
  orderTimeoutMinutes: number;
  /** 重复支付检查时间窗口（分钟） */
  duplicateCheckWindowMinutes: number;
}

export interface PaymentOrderConfig {
  /** 订单ID前缀 */
  orderIdPrefix: string;
  /** 订单状态超时时间（分钟） */
  statusTimeoutMinutes: number;
  /** 最大重试次数 */
  maxRetryCount: number;
  /** 自动取消未支付订单时间（分钟） */
  autoCancelMinutes: number;
}

export interface PaymentAuditConfig {
  /** 是否启用审计日志 */
  enabled: boolean;
  /** 日志保留天数 */
  retentionDays: number;
  /** 敏感信息脱敏 */
  maskSensitiveData: boolean;
  /** 详细日志级别 */
  detailLevel: 'basic' | 'detailed' | 'full';
}

export interface AppConfig {
  /** 服务基础配置 */
  service: {
    name: string;
    version: string;
    environment: string;
    port: number;
  };

  /** 支付网关配置 */
  gateways: PaymentGatewayConfig;

  /** 安全配置 */
  security: PaymentSecurityConfig;

  /** 订单配置 */
  order: PaymentOrderConfig;

  /** 审计配置 */
  audit: PaymentAuditConfig;

  /** 通知配置 */
  notification: {
    /** 支付成功通知服务 */
    successNotifyService: string;
    /** 支付失败通知服务 */
    failureNotifyService: string;
    /** 通知重试次数 */
    notifyRetryCount: number;
    /** 通知超时时间（毫秒） */
    notifyTimeoutMs: number;
  };

  /** 报告配置 */
  report: {
    /** 是否启用自动报告 */
    autoReportEnabled: boolean;
    /** 报告生成时间（cron表达式） */
    reportCron: string;
    /** 报告保留天数 */
    reportRetentionDays: number;
  };
}

export default registerAs('payment', (): AppConfig => ({
  service: {
    name: process.env.APP_NAME || 'payment',
    version: process.env.PAYMENT_SERVICE_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PAYMENT_PORT || '3006', 10),
  },

  gateways: {
    alipay: {
      appId: process.env.ALIPAY_APP_ID || '',
      privateKey: process.env.ALIPAY_PRIVATE_KEY || '',
      publicKey: process.env.ALIPAY_PUBLIC_KEY || '',
      gatewayUrl: process.env.ALIPAY_GATEWAY_URL || 'https://openapi.alipay.com/gateway.do',
      notifyUrl: process.env.ALIPAY_NOTIFY_URL || '',
      returnUrl: process.env.ALIPAY_RETURN_URL || '',
      signType: 'RSA2',
      charset: 'utf-8',
      version: '1.0',
    },

    wechat: {
      appId: process.env.WECHAT_APP_ID || '',
      mchId: process.env.WECHAT_MCH_ID || '',
      apiKey: process.env.WECHAT_API_KEY || '',
      certPath: process.env.WECHAT_CERT_PATH || '',
      keyPath: process.env.WECHAT_KEY_PATH || '',
      notifyUrl: process.env.WECHAT_NOTIFY_URL || '',
      apiVersion: 'v3',
    },

    apple: {
      bundleId: process.env.APPLE_BUNDLE_ID || '',
      environment: (process.env.APPLE_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
      sharedSecret: process.env.APPLE_SHARED_SECRET || '',
      verifyReceiptUrl: process.env.APPLE_VERIFY_URL || 'https://sandbox.itunes.apple.com/verifyReceipt',
    },
  },

  security: {
    signSecret: process.env.PAYMENT_SIGN_SECRET || 'default-sign-secret',
    encryptSecret: process.env.PAYMENT_ENCRYPT_SECRET || 'default-encrypt-secret',
    signAlgorithm: 'SHA256',
    encryptAlgorithm: 'AES-256-CBC',
    callbackWhitelist: (process.env.PAYMENT_CALLBACK_WHITELIST || '').split(',').filter(Boolean),
    orderTimeoutMinutes: parseInt(process.env.PAYMENT_ORDER_TIMEOUT_MINUTES || '30', 10),
    duplicateCheckWindowMinutes: parseInt(process.env.PAYMENT_DUPLICATE_CHECK_MINUTES || '5', 10),
  },

  order: {
    orderIdPrefix: process.env.PAYMENT_ORDER_PREFIX || 'PAY',
    statusTimeoutMinutes: parseInt(process.env.PAYMENT_STATUS_TIMEOUT_MINUTES || '60', 10),
    maxRetryCount: parseInt(process.env.PAYMENT_MAX_RETRY_COUNT || '3', 10),
    autoCancelMinutes: parseInt(process.env.PAYMENT_AUTO_CANCEL_MINUTES || '1440', 10), // 24小时
  },

  audit: {
    enabled: process.env.PAYMENT_AUDIT_ENABLED === 'true',
    retentionDays: parseInt(process.env.PAYMENT_AUDIT_RETENTION_DAYS || '90', 10),
    maskSensitiveData: process.env.PAYMENT_MASK_SENSITIVE_DATA !== 'false',
    detailLevel: (process.env.PAYMENT_AUDIT_DETAIL_LEVEL as 'basic' | 'detailed' | 'full') || 'detailed',
  },

  notification: {
    successNotifyService: process.env.PAYMENT_SUCCESS_NOTIFY_SERVICE || 'character',
    failureNotifyService: process.env.PAYMENT_FAILURE_NOTIFY_SERVICE || 'character',
    notifyRetryCount: parseInt(process.env.PAYMENT_NOTIFY_RETRY_COUNT || '3', 10),
    notifyTimeoutMs: parseInt(process.env.PAYMENT_NOTIFY_TIMEOUT_MS || '5000', 10),
  },

  report: {
    autoReportEnabled: process.env.PAYMENT_AUTO_REPORT_ENABLED === 'true',
    reportCron: process.env.PAYMENT_REPORT_CRON || '0 2 * * *', // 每天凌晨2点
    reportRetentionDays: parseInt(process.env.PAYMENT_REPORT_RETENTION_DAYS || '365', 10),
  },
}));
