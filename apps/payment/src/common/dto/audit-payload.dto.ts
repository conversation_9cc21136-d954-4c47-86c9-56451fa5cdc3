/**
 * Audit模块的Payload DTO定义
 * 
 * 为audit.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，采用扁平化设计，不嵌套其他DTO
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsObject, IsDateString, IsBoolean, IsNumber, IsArray, Length, Min, Max } from 'class-validator';
import { Expose, Transform, Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

/**
 * 审计事件类型枚举
 */
export enum AuditEventType {
  ORDER_CREATED = 'ORDER_CREATED',
  ORDER_STATUS_CHANGED = 'ORDER_STATUS_CHANGED',
  PAYMENT_REQUEST = 'PAYMENT_REQUEST',
  PAYMENT_CALLBACK = 'PAYMENT_CALLBACK',
  PAYMENT_SUCCESS = 'PAYMENT_SUCCESS',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  SECURITY_VALIDATION = 'SECURITY_VALIDATION',
  SECURITY_EVENT = 'SECURITY_EVENT',
  DATA_ACCESS = 'DATA_ACCESS',
  DATA_MODIFICATION = 'DATA_MODIFICATION',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  USER_OPERATION = 'USER_OPERATION',
  ADMIN_OPERATION = 'ADMIN_OPERATION',
}

/**
 * 审计级别枚举
 */
export enum AuditLevel {
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL',
}

/**
 * 记录审计日志Payload DTO
 * @MessagePattern('payment.audit.record')
 * 扁平化设计，直接包含审计记录字段
 */
export class RecordAuditPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '事件类型', enum: AuditEventType, example: AuditEventType.PAYMENT_SUCCESS })
  @Expose()
  @IsEnum(AuditEventType, { message: '事件类型不正确' })
  eventType: AuditEventType;

  @ApiProperty({ description: '审计级别', enum: AuditLevel, example: AuditLevel.INFO })
  @Expose()
  @IsEnum(AuditLevel, { message: '审计级别不正确' })
  level: AuditLevel;

  @ApiProperty({ description: '事件描述', example: '支付订单创建成功' })
  @Expose()
  @IsString()
  @Length(1, 500, { message: '事件描述长度必须在1-500之间' })
  description: string;

  @ApiPropertyOptional({ description: '详细消息' })
  @Expose()
  @IsOptional()
  @IsString()
  @Length(0, 1000, { message: '详细消息长度不能超过1000' })
  message?: string;

  @ApiPropertyOptional({ description: '用户ID' })
  @Expose()
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({ description: '角色ID' })
  @Expose()
  @IsOptional()
  @IsString()
  characterId?: string;

  @ApiPropertyOptional({ description: '订单号' })
  @Expose()
  @IsOptional()
  @IsString()
  orderNo?: string;

  @ApiPropertyOptional({ description: '交易号' })
  @Expose()
  @IsOptional()
  @IsString()
  transactionId?: string;

  @ApiPropertyOptional({ description: '支付渠道' })
  @Expose()
  @IsOptional()
  @IsString()
  paymentChannel?: string;

  @ApiPropertyOptional({ description: '客户端IP' })
  @Expose()
  @IsOptional()
  @IsString()
  clientIp?: string;

  @ApiPropertyOptional({ description: '用户代理' })
  @Expose()
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiPropertyOptional({ description: '请求数据' })
  @Expose()
  @IsOptional()
  @IsObject()
  requestData?: Record<string, any>;

  @ApiPropertyOptional({ description: '响应数据' })
  @Expose()
  @IsOptional()
  @IsObject()
  responseData?: Record<string, any>;

  @ApiPropertyOptional({ description: '错误信息' })
  @Expose()
  @IsOptional()
  @IsString()
  errorMessage?: string;

  @ApiPropertyOptional({ description: '错误堆栈' })
  @Expose()
  @IsOptional()
  @IsString()
  errorStack?: string;

  @ApiPropertyOptional({ description: '处理时间（毫秒）' })
  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  processingTime?: number;

  @ApiPropertyOptional({ description: '是否成功', default: true })
  @Expose()
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  success?: boolean = true;

  @ApiPropertyOptional({ description: '风险等级', enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'] })
  @Expose()
  @IsOptional()
  @IsEnum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
  riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

  @ApiPropertyOptional({ description: '扩展数据' })
  @Expose()
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

// QueryAuditLogsPayloadDto已删除 - 对应的payment.audit.query接口已删除

// 以下DTO已删除 - 对应的接口已删除：
// - GetAuditStatsPayloadDto (payment.audit.getStats)
// - GetOrderAuditTrailPayloadDto (payment.audit.getOrderTrail)
// - GetSecurityEventsPayloadDto (payment.audit.getSecurityEvents)
// - GenerateComplianceReportPayloadDto (payment.audit.generateComplianceReport)
