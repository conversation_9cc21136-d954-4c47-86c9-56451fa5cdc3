/**
 * Callback模块的Payload DTO定义
 * 
 * 为callback.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，采用扁平化设计，不嵌套其他DTO
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsObject, IsDateString, Length } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { PaymentChannel } from '../schemas/order.schema';

/**
 * 处理支付回调Payload DTO
 * @MessagePattern('payment.callback.process')
 * 扁平化设计，直接包含回调处理字段
 */
export class ProcessCallbackPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '支付渠道', enum: PaymentChannel, example: PaymentChannel.ALIPAY })
  @Expose()
  @IsEnum(PaymentChannel, { message: '支付渠道不正确' })
  channel: PaymentChannel;

  @ApiProperty({ description: '回调数据' })
  @Expose()
  @IsObject()
  callbackData: Record<string, any>;
}

/**
 * 通知业务系统Payload DTO
 * @MessagePattern('payment.callback.notify')
 * 扁平化设计，直接包含通知字段
 */
export class NotifyBusinessSystemPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '订单号', example: 'ORDER_20231201_001' })
  @Expose()
  @IsString()
  @Length(1, 100, { message: '订单号长度必须在1-100之间' })
  orderNo: string;

  @ApiPropertyOptional({ description: '通知类型', enum: ['success', 'failed'], default: 'success' })
  @Expose()
  @IsOptional()
  @IsEnum(['success', 'failed'], { message: '通知类型不正确' })
  notifyType?: 'success' | 'failed' = 'success';
}

// 以下DTO已删除 - 对应的管理接口已删除：
// - RetryCallbackPayloadDto (payment.callback.retry)
// - GetCallbackStatsPayloadDto (payment.callback.getStats)
