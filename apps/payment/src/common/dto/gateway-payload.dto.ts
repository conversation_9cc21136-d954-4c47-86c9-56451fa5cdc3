/**
 * Gateway模块的Payload DTO定义
 * 
 * 为gateway.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，采用扁平化设计，不嵌套其他DTO
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsObject, IsNumber, IsUrl, Length, Min, Max } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { PaymentChannel } from '../schemas/order.schema';

/**
 * 创建支付Payload DTO
 * @MessagePattern('payment.gateway.createPayment')
 * 扁平化设计，直接包含支付请求的所有字段
 */
export class CreatePaymentPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '支付渠道', enum: PaymentChannel, example: PaymentChannel.ALIPAY })
  @Expose()
  @IsEnum(PaymentChannel, { message: '支付渠道不正确' })
  channel: PaymentChannel;

  @ApiProperty({ description: '订单号', example: 'ORDER_20231201_001' })
  @Expose()
  @IsString()
  @Length(1, 100, { message: '订单号长度必须在1-100之间' })
  orderNo: string;

  @ApiProperty({ description: '订单金额（分）', example: 100, minimum: 1 })
  @Expose()
  @IsNumber({}, { message: '订单金额必须是数字' })
  @Min(1, { message: '订单金额必须大于0' })
  @Max(999999999, { message: '订单金额不能超过9999999.99元' })
  amount: number;

  @ApiPropertyOptional({ description: '货币类型', example: 'CNY', default: 'CNY' })
  @Expose()
  @IsOptional()
  @IsString()
  @Length(3, 3, { message: '货币类型必须是3位字符' })
  currency?: string = 'CNY';

  @ApiProperty({ description: '订单标题', example: '游戏充值' })
  @Expose()
  @IsString()
  @Length(1, 100, { message: '订单标题长度必须在1-100之间' })
  title: string;

  @ApiPropertyOptional({ description: '订单描述', example: '充值100金币' })
  @Expose()
  @IsOptional()
  @IsString()
  @Length(0, 500, { message: '订单描述长度不能超过500' })
  description?: string;

  @ApiPropertyOptional({ description: '异步通知地址' })
  @Expose()
  @IsOptional()
  @IsUrl({}, { message: '异步通知地址格式不正确' })
  notifyUrl?: string;

  @ApiPropertyOptional({ description: '同步跳转地址' })
  @Expose()
  @IsOptional()
  @IsUrl({}, { message: '同步跳转地址格式不正确' })
  returnUrl?: string;

  @ApiPropertyOptional({ description: '扩展数据' })
  @Expose()
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 查询支付状态Payload DTO
 * @MessagePattern('payment.gateway.queryPayment')
 * 扁平化设计，直接包含查询字段
 */
export class QueryPaymentPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '支付渠道', enum: PaymentChannel, example: PaymentChannel.ALIPAY })
  @Expose()
  @IsEnum(PaymentChannel, { message: '支付渠道不正确' })
  channel: PaymentChannel;

  @ApiProperty({ description: '第三方交易号', example: 'alipay_txn_20231201_001' })
  @Expose()
  @IsString()
  @Length(1, 200, { message: '第三方交易号长度必须在1-200之间' })
  transactionId: string;
}

/**
 * 验证支付回调Payload DTO
 * @MessagePattern('payment.gateway.verifyCallback')
 * 扁平化设计，直接包含回调验证字段
 */
export class VerifyCallbackPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '支付渠道', enum: PaymentChannel, example: PaymentChannel.ALIPAY })
  @Expose()
  @IsEnum(PaymentChannel, { message: '支付渠道不正确' })
  channel: PaymentChannel;

  @ApiProperty({ description: '回调数据' })
  @Expose()
  @IsObject()
  callbackData: Record<string, any>;
}

/**
 * 验证苹果支付收据Payload DTO
 * @MessagePattern('payment.gateway.verifyAppleReceipt')
 * 扁平化设计，直接包含苹果收据验证字段
 */
export class VerifyAppleReceiptPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '收据数据（Base64编码）' })
  @Expose()
  @IsString()
  @Length(1, 10000, { message: '收据数据长度必须在1-10000之间' })
  receiptData: string;

  @ApiProperty({ description: '订单号', example: 'ORDER_20231201_001' })
  @Expose()
  @IsString()
  @Length(1, 100, { message: '订单号长度必须在1-100之间' })
  orderNo: string;
}

// 以下DTO已删除 - 对应的管理接口已删除：
// - GetSupportedChannelsPayloadDto (payment.gateway.getSupportedChannels)
// - CheckChannelAvailabilityPayloadDto (payment.gateway.checkChannelAvailability)
// - GetChannelConfigPayloadDto (payment.gateway.getChannelConfig)
// - TestConnectionPayloadDto (payment.gateway.testConnection)
// - GetGatewayStatsPayloadDto (payment.gateway.getStats)
