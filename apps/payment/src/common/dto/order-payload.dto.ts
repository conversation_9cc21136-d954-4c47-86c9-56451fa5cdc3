/**
 * Order模块的Payload DTO定义
 *
 * 为order.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，采用扁平化设计，不嵌套其他DTO
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsEnum, IsOptional, IsBoolean, IsDateString, IsObject, Min, Max, Length, IsIP } from 'class-validator';
import { Expose, Transform, Type } from 'class-transformer';
import { PaymentChannel, PaymentMethod, PaymentOrderStatus } from '../schemas/order.schema';
import { BasePayloadDto } from "@libs/common";

/**
 * 创建支付订单Payload DTO
 * @MessagePattern('payment.order.create')
 * 扁平化设计，直接包含创建订单的所有字段
 */
export class CreatePaymentOrderPayload extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: '507f1f77bcf86cd799439011' })
  @Expose()
  @IsString()
  @Length(24, 24, { message: '角色ID格式不正确' })
  characterId: string;

  @ApiProperty({ description: '用户ID', example: '507f1f77bcf86cd799439012' })
  @Expose()
  @IsString()
  @Length(24, 24, { message: '用户ID格式不正确' })
  userId: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString()
  @Length(1, 50, { message: '服务器ID长度必须在1-50之间' })
  serverId: string;

  @ApiProperty({ description: '充值配置ID', example: 1 })
  @Expose()
  @IsNumber({}, { message: '充值配置ID必须是数字' })
  @Min(1, { message: '充值配置ID必须大于0' })
  rechargeId: number;

  @ApiProperty({ description: '订单金额（分）', example: 100, minimum: 1 })
  @Expose()
  @IsNumber({}, { message: '订单金额必须是数字' })
  @Min(1, { message: '订单金额必须大于0' })
  @Max(999999999, { message: '订单金额不能超过9999999.99元' })
  amount: number;

  @ApiPropertyOptional({ description: '货币类型', example: 'CNY', default: 'CNY' })
  @Expose()
  @IsOptional()
  @IsString()
  @Length(3, 3, { message: '货币类型必须是3位字符' })
  currency?: string = 'CNY';

  @ApiProperty({ description: '支付渠道', enum: PaymentChannel, example: PaymentChannel.ALIPAY })
  @Expose()
  @IsEnum(PaymentChannel, { message: '支付渠道不正确' })
  channel: PaymentChannel;

  @ApiProperty({ description: '支付方式', enum: PaymentMethod, example: PaymentMethod.QR_CODE })
  @Expose()
  @IsEnum(PaymentMethod, { message: '支付方式不正确' })
  method: PaymentMethod;

  @ApiProperty({ description: '订单标题', example: '游戏充值' })
  @Expose()
  @IsString()
  @Length(1, 100, { message: '订单标题长度必须在1-100之间' })
  title: string;

  @ApiPropertyOptional({ description: '订单描述', example: '充值100金币' })
  @Expose()
  @IsOptional()
  @IsString()
  @Length(0, 500, { message: '订单描述长度不能超过500' })
  description?: string;

  @ApiPropertyOptional({ description: '客户端IP', example: '***********' })
  @Expose()
  @IsOptional()
  @IsIP(undefined, { message: '客户端IP格式不正确' })
  clientIp?: string;

  @ApiPropertyOptional({ description: '用户代理' })
  @Expose()
  @IsOptional()
  @IsString()
  @Length(0, 500, { message: '用户代理长度不能超过500' })
  userAgent?: string;

  @ApiPropertyOptional({ description: '设备信息' })
  @Expose()
  @IsOptional()
  @IsObject()
  deviceInfo?: Record<string, any>;

  @ApiPropertyOptional({ description: '扩展数据' })
  @Expose()
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 查询支付订单Payload DTO
 * @MessagePattern('payment.order.query')
 * 扁平化设计，直接包含查询条件字段
 */
export class QueryPaymentOrderPayload extends BasePayloadDto {
  @ApiPropertyOptional({ description: '订单号' })
  @Expose()
  @IsOptional()
  @IsString()
  orderNo?: string;

  @ApiPropertyOptional({ description: '角色ID' })
  @Expose()
  @IsOptional()
  @IsString()
  characterId?: string;

  @ApiPropertyOptional({ description: '用户ID' })
  @Expose()
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({ description: '服务器ID' })
  @Expose()
  @IsOptional()
  @IsString()
  serverId?: string;

  @ApiPropertyOptional({ description: '支付渠道', enum: PaymentChannel })
  @Expose()
  @IsOptional()
  @IsEnum(PaymentChannel)
  channel?: PaymentChannel;

  @ApiPropertyOptional({ description: '订单状态', enum: PaymentOrderStatus })
  @Expose()
  @IsOptional()
  @IsEnum(PaymentOrderStatus)
  status?: PaymentOrderStatus;

  @ApiPropertyOptional({ description: '开始时间', example: '2023-01-01T00:00:00.000Z' })
  @Expose()
  @IsOptional()
  @IsDateString()
  startTime?: string;

  @ApiPropertyOptional({ description: '结束时间', example: '2023-12-31T23:59:59.999Z' })
  @Expose()
  @IsOptional()
  @IsDateString()
  endTime?: string;

  @ApiPropertyOptional({ description: '页码', example: 1, minimum: 1, default: 1 })
  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码必须大于0' })
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', example: 10, minimum: 1, maximum: 100, default: 10 })
  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number = 10;
}

/**
 * 更新支付订单状态Payload DTO
 * @MessagePattern('payment.order.updateStatus')
 * 扁平化设计，直接包含更新状态字段
 */
export class UpdatePaymentOrderStatusPayload extends BasePayloadDto {
  @ApiProperty({ description: '订单号' })
  @Expose()
  @IsString()
  orderNo: string;

  @ApiProperty({ description: '新状态', enum: PaymentOrderStatus })
  @Expose()
  @IsEnum(PaymentOrderStatus, { message: '订单状态不正确' })
  status: PaymentOrderStatus;

  @ApiProperty({ description: '状态变更原因' })
  @Expose()
  @IsString()
  @Length(1, 200, { message: '状态变更原因长度必须在1-200之间' })
  reason: string;

  @ApiPropertyOptional({ description: '操作者' })
  @Expose()
  @IsOptional()
  @IsString()
  operator?: string;

  @ApiPropertyOptional({ description: '额外信息' })
  @Expose()
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 支付回调Payload DTO
 * @MessagePattern('payment.order.callback')
 * 扁平化设计，直接包含回调数据字段
 */
export class PaymentCallbackPayload extends BasePayloadDto {
  @ApiProperty({ description: '订单号' })
  @Expose()
  @IsString()
  orderNo: string;

  @ApiProperty({ description: '支付渠道', enum: PaymentChannel })
  @Expose()
  @IsEnum(PaymentChannel, { message: '支付渠道不正确' })
  channel: PaymentChannel;

  @ApiProperty({ description: '第三方交易号' })
  @Expose()
  @IsString()
  transactionId: string;

  @ApiProperty({ description: '支付状态' })
  @Expose()
  @IsString()
  paymentStatus: string;

  @ApiPropertyOptional({ description: '支付金额（分）' })
  @Expose()
  @IsOptional()
  @IsNumber()
  @Min(0)
  amount?: number;

  @ApiPropertyOptional({ description: '支付时间' })
  @Expose()
  @IsOptional()
  @IsDateString()
  paidAt?: string;

  @ApiPropertyOptional({ description: '客户端IP' })
  @Expose()
  @IsOptional()
  @IsIP()
  clientIp?: string;

  @ApiPropertyOptional({ description: '原始回调数据' })
  @Expose()
  @IsOptional()
  @IsObject()
  rawData?: Record<string, any>;
}

/**
 * 验证支付Payload DTO
 * @MessagePattern('payment.order.verify')
 * 扁平化设计，直接包含验证字段
 */
export class VerifyPaymentPayload extends BasePayloadDto {
  @ApiProperty({ description: '订单号' })
  @Expose()
  @IsString()
  orderNo: string;

  @ApiProperty({ description: '支付渠道', enum: PaymentChannel })
  @Expose()
  @IsEnum(PaymentChannel, { message: '支付渠道不正确' })
  channel: PaymentChannel;

  @ApiPropertyOptional({ description: '第三方交易号' })
  @Expose()
  @IsOptional()
  @IsString()
  transactionId?: string;

  @ApiPropertyOptional({ description: '强制验证', default: false })
  @Expose()
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  forceVerify?: boolean = false;
}

/**
 * 获取订单详情Payload DTO
 * @MessagePattern('payment.order.getDetail')
 */
export class GetOrderDetailPayload extends BasePayloadDto {
  @ApiProperty({ description: '订单号' })
  @Expose()
  @IsString()
  orderNo: string;
}

/**
 * 取消订单Payload DTO
 * @MessagePattern('payment.order.cancel')
 */
export class CancelOrderPayload extends BasePayloadDto {
  @ApiProperty({ description: '订单号' })
  @Expose()
  @IsString()
  orderNo: string;

  @ApiPropertyOptional({ description: '取消原因' })
  @Expose()
  @IsOptional()
  @IsString()
  @Length(1, 200, { message: '取消原因长度必须在1-200之间' })
  reason?: string;

  @ApiPropertyOptional({ description: '操作者' })
  @Expose()
  @IsOptional()
  @IsString()
  operator?: string;
}

/**
 * 根据交易号获取订单Payload DTO
 * @MessagePattern('payment.order.getByTransactionId')
 */
export class GetOrderByTransactionIdPayload extends BasePayloadDto {
  @ApiProperty({ description: '第三方交易号', example: 'alipay_txn_20231201_001' })
  @Expose()
  @IsString()
  @Length(1, 200, { message: '第三方交易号长度必须在1-200之间' })
  transactionId: string;
}

/**
 * 获取角色订单列表Payload DTO
 * @MessagePattern('payment.order.getCharacterOrders')
 */
export class GetCharacterOrdersPayload extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: '507f1f77bcf86cd799439011' })
  @Expose()
  @IsString()
  @Length(24, 24, { message: '角色ID格式不正确' })
  characterId: string;

  @ApiPropertyOptional({ description: '限制数量', example: 10, minimum: 1, maximum: 100, default: 10 })
  @Expose()
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '限制数量必须是数字' })
  @Min(1, { message: '限制数量必须大于0' })
  @Max(100, { message: '限制数量不能超过100' })
  limit?: number = 10;

  @ApiPropertyOptional({ description: '订单状态', enum: PaymentOrderStatus })
  @Expose()
  @IsOptional()
  @IsEnum(PaymentOrderStatus)
  status?: PaymentOrderStatus;
}

// 以下DTO已删除 - 对应的接口已删除：
// - GetOrderStatsPayload (payment.order.getStats)
// - CheckOrderStatusPayload (payment.order.checkStatus)

/**
 * 重试订单Payload DTO
 * @MessagePattern('payment.order.retry')
 */
export class RetryOrderPayload extends BasePayloadDto {
  @ApiProperty({ description: '订单号' })
  @Expose()
  @IsString()
  orderNo: string;

  @ApiProperty({ description: '重试原因' })
  @Expose()
  @IsString()
  @Length(1, 200, { message: '重试原因长度必须在1-200之间' })
  reason: string;

  @ApiPropertyOptional({ description: '操作者' })
  @Expose()
  @IsOptional()
  @IsString()
  operator?: string;
}
