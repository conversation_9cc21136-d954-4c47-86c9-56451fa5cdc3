/**
 * 支付安全服务统一导出
 * 
 * 提供支付系统所需的核心安全服务：
 * - SignatureService - 签名生成和验证
 * - RateLimitService - 频率限制控制
 * - EncryptionService - 数据加密解密
 * - RiskControlService - 风险评估控制
 */

export { SignatureService } from './signature.service';
export { RateLimitService } from './rate-limit.service';
export { EncryptionService } from './encryption.service';
export { RiskControlService } from './risk-control.service';

/**
 * 安全服务提供者数组
 * 用于在模块中批量注册安全服务
 */
export const SECURITY_SERVICES = [
  SignatureService,
  RateLimitService,
  EncryptionService,
  RiskControlService,
];
