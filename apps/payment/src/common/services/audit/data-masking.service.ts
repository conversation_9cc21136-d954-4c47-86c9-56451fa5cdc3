/**
 * 支付数据脱敏Service（基础服务版）
 * 
 * 负责敏感数据的脱敏处理，供其他业务服务使用：
 * - 敏感字段识别
 * - 数据脱敏规则
 * - 脱敏算法实现
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseService } from '@libs/common/service/base-service';
import { AppConfig } from '../../../config/app.config';

@Injectable()
export class DataMaskingService extends BaseService {
  protected readonly logger = new Logger(DataMaskingService.name);
  private readonly config: AppConfig['audit'];

  // 敏感字段列表
  private readonly sensitiveFields = [
    'password',
    'secret',
    'key',
    'token',
    'sign',
    'signature',
    'cardNo',
    'bankCard',
    'phone',
    'mobile',
    'email',
    'idCard',
    'realName',
    'address',
  ];

  constructor(private readonly configService: ConfigService) {
    super(DataMaskingService.name);
    this.config = this.configService.get<AppConfig>('payment')?.audit;
  }

  /**
   * 脱敏敏感数据
   */
  async maskSensitiveData(data: Record<string, any>): Promise<Record<string, any>> {
    if (!this.config?.maskSensitiveData) {
      return data;
    }

    const maskedData = { ...data };

    for (const [key, value] of Object.entries(maskedData)) {
      if (this.isSensitiveField(key) && typeof value === 'string') {
        maskedData[key] = this.maskValue(value, key);
      } else if (typeof value === 'object' && value !== null) {
        maskedData[key] = await this.maskSensitiveData(value);
      }
    }

    return maskedData;
  }

  /**
   * 检查是否为敏感字段
   */
  private isSensitiveField(fieldName: string): boolean {
    const lowerFieldName = fieldName.toLowerCase();
    return this.sensitiveFields.some(sensitiveField => 
      lowerFieldName.includes(sensitiveField)
    );
  }

  /**
   * 脱敏值
   */
  private maskValue(value: string, fieldType: string): string {
    if (!value || value.length === 0) {
      return value;
    }

    const lowerFieldType = fieldType.toLowerCase();

    // 根据字段类型使用不同的脱敏策略
    if (lowerFieldType.includes('phone') || lowerFieldType.includes('mobile')) {
      return this.maskPhone(value);
    } else if (lowerFieldType.includes('email')) {
      return this.maskEmail(value);
    } else if (lowerFieldType.includes('card')) {
      return this.maskCardNumber(value);
    } else if (lowerFieldType.includes('name')) {
      return this.maskName(value);
    } else {
      return this.maskGeneral(value);
    }
  }

  /**
   * 脱敏手机号
   */
  private maskPhone(phone: string): string {
    if (phone.length < 7) return '***';
    return phone.substring(0, 3) + '****' + phone.substring(phone.length - 4);
  }

  /**
   * 脱敏邮箱
   */
  private maskEmail(email: string): string {
    const atIndex = email.indexOf('@');
    if (atIndex <= 0) return '***@***';
    
    const username = email.substring(0, atIndex);
    const domain = email.substring(atIndex);
    
    if (username.length <= 2) {
      return '*'.repeat(username.length) + domain;
    }
    
    return username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1) + domain;
  }

  /**
   * 脱敏卡号
   */
  private maskCardNumber(cardNo: string): string {
    if (cardNo.length < 8) return '****';
    return cardNo.substring(0, 4) + '*'.repeat(cardNo.length - 8) + cardNo.substring(cardNo.length - 4);
  }

  /**
   * 脱敏姓名
   */
  private maskName(name: string): string {
    if (name.length <= 1) return '*';
    if (name.length === 2) return name.charAt(0) + '*';
    return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1);
  }

  /**
   * 通用脱敏
   */
  private maskGeneral(value: string): string {
    if (value.length <= 4) return '***';
    return value.substring(0, 2) + '*'.repeat(value.length - 4) + value.substring(value.length - 2);
  }
}
