/**
 * 支付审计日志Service（基础服务版）
 *
 * 负责审计日志的核心功能，供其他业务服务使用：
 * - 审计日志创建
 * - 订单审计轨迹查询
 * - 基础维护功能
 */

import { Injectable, Logger } from '@nestjs/common';
import { BaseService } from '@libs/common/service/base-service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { PaymentAuditLog, AuditEventType, AuditLevel } from '../../schemas/audit-log.schema';
import { AuditLogRepository } from '../../repositories/audit-log.repository';

@Injectable()
export class AuditLogService extends BaseService {
  constructor(
    private readonly auditLogRepository: AuditLogRepository
  ) {
    super(AuditLogService.name);
  }

  /**
   * 创建审计日志（核心功能）
   */
  async createAuditLog(logData: Partial<PaymentAuditLog>): Promise<XResult<PaymentAuditLog>> {
    return this.executeBusinessOperation(async () => {
      const result = await this.auditLogRepository.createAuditLog(logData);
      if (XResultUtils.isFailure(result)) {
        return XResultUtils.error(`创建审计日志失败: ${result.message}`, 'CREATE_AUDIT_LOG_FAILED');
      }
      return XResultUtils.ok(result.data);
    }, { reason: 'create_audit_log' });
  }

  /**
   * 获取订单审计轨迹（业务需要）
   */
  async getOrderAuditTrail(orderNo: string): Promise<XResult<PaymentAuditLog[]>> {
    return this.executeBusinessOperation(async () => {
      const result = await this.auditLogRepository.getOrderAuditTrail(orderNo);
      if (XResultUtils.isFailure(result)) {
        return XResultUtils.error(`获取订单审计轨迹失败: ${result.message}`, 'GET_ORDER_AUDIT_TRAIL_FAILED');
      }
      return XResultUtils.ok(result.data);
    }, { reason: 'get_order_audit_trail', metadata: { orderNo } });
  }

  /**
   * 清理过期日志（维护功能）
   */
  async cleanupExpiredLogs(): Promise<XResult<number>> {
    return this.executeBusinessOperation(async () => {
      const result = await this.auditLogRepository.cleanupExpiredLogs();
      if (XResultUtils.isFailure(result)) {
        return XResultUtils.error(`清理过期日志失败: ${result.message}`, 'CLEANUP_EXPIRED_LOGS_FAILED');
      }
      return XResultUtils.ok(result.data);
    }, { reason: 'cleanup_expired_logs' });
  }
}
