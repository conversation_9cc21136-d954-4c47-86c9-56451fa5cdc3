/**
 * 支付报告Controller
 * 
 * 提供支付报告相关的微服务接口，包括：
 * - 报告生成和管理
 * - 报告查询和统计
 * - 报告导出和下载
 * - 对账处理
 * - 报告模板管理
 */

import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ApiTags } from '@nestjs/swagger';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import { XResponse, XResponseUtils } from '@libs/common/types/result.type';
import { ReportService, GenerateReportResult, ReconciliationResult } from './report.service';
import { ReportDocument } from '../../common/schemas/report.schema';
import { ReportStatistics } from '../../common/repositories/report.repository';
import { GenerateReportDto, QueryReportDto } from '../../common/dto/report.dto';
import {
  GenerateReportPayloadDto,
  QueryReportPayloadDto,
  GetReportDetailPayloadDto,
} from '../../common/dto/report-payload.dto';

/**
 * 支付报告Controller
 * 
 * 继承BaseController，获得完整的微服务接口处理框架
 */
@ApiTags('payment-report')
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class ReportController extends BaseController {
  constructor(private readonly reportService: ReportService) {
    super(ReportController.name);
  }

  // ==================== 1. 报告生成相关 ====================

  /**
   * 生成报告
   */
  @MessagePattern('report.generate')
  async generateReport(@Payload() payload: GenerateReportPayloadDto): Promise<XResponse<GenerateReportResult>> {
    this.logger.log(`Generate report request - type: ${payload.type}`);

    // 构建GenerateReportDto
    const generateDto: GenerateReportDto = {
      type: payload.type,
      format: payload.format,
      startTime: payload.startTime,
      endTime: payload.endTime,
      title: payload.title,
      description: payload.description,
      includeReconciliation: payload.includeReconciliation,
      includeExceptions: payload.includeExceptions,
    };

    const result = await this.reportService.generateReport(generateDto);
    return XResponseUtils.fromResult(result, 'Report generation completed');
  }

  /**
   * 查询报告列表
   */
  @MessagePattern('report.query')
  async queryReports(@Payload() payload: QueryReportPayloadDto): Promise<XResponse<{
    reports: ReportDocument[];
    total: number;
    page: number;
    limit: number;
  }>> {
    this.logger.log('Query reports list');

    // 构建QueryReportDto
    const queryDto: QueryReportDto = {
      type: payload.type,
      status: payload.status,
      startTime: payload.startTime,
      endTime: payload.endTime,
      page: payload.page,
      limit: payload.limit,
    };

    const result = await this.reportService.queryReports(queryDto);
    return XResponseUtils.fromResult(result, 'Reports list query successful');
  }

  /**
   * 获取报告详情
   */
  @MessagePattern('report.getDetail')
  async getReportDetail(@Payload() payload: GetReportDetailPayloadDto): Promise<XResponse<ReportDocument | null>> {
    this.logger.log(`Get report detail - reportId: ${payload.reportId}`);

    const result = await this.reportService.getReportDetail(payload.reportId);
    return XResponseUtils.fromResult(result, 'Get report detail successful');
  }

  // 其他报告功能（删除、统计、对账、导出、模板管理等）已移除
  // 这些功能应该在管理后台通过直接调用ReportService实现
  // 支付服务只需要核心的报告生成和查询功能
}

