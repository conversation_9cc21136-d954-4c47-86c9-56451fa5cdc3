/**
 * 支付安全Controller（精简版）
 *
 * 专注于支付业务相关的安全验证：
 * - 支付回调签名验证
 * - IP白名单检查
 * - 频率限制和风控
 *
 * 注意：通用安全功能已移至Gateway层处理
 */

import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ApiTags } from '@nestjs/swagger';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import { XResponse, XResponseUtils } from '@libs/common/types/result.type';
import { SecurityService } from './security.service';
import {
  ValidatePaymentCallbackPayloadDto,
  CheckIpWhitelistPayloadDto,
  CheckRateLimitPayloadDto,
} from '../../common/dto/security-payload.dto';

@ApiTags('payment-security')
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class SecurityController extends BaseController {
  constructor(private readonly securityService: SecurityService) {
    super(SecurityController.name);
  }

  /**
   * 安全验证
   */
  @MessagePattern('payment.security.validate')
  async validateSecurity(@Payload() payload: ValidateSecurityPayloadDto): Promise<XResponse<SecurityValidationResult>> {
    this.logger.log(`Security validation - IP: ${payload.clientIp}`);

    // 构建SecurityValidationRequest
    const request: SecurityValidationRequest = {
      clientIp: payload.clientIp,
      userAgent: payload.userAgent,
      requestPath: payload.requestPath,
      requestMethod: payload.requestMethod,
      requestData: payload.requestData,
      userId: payload.userId,
      characterId: payload.characterId,
    };

    const result = await this.securityService.validateSecurity(request);
    return this.fromResult(result, 'Security validation completed');
  }

  /**
   * 验证签名
   */
  @MessagePattern('payment.security.verifySignature')
  async verifySignature(@Payload() payload: VerifySignaturePayloadDto): Promise<XResponse<boolean>> {
    this.logger.log('Verify signature');

    const result = await this.securityService.validateSignature(
      payload.data,
      payload.signature,
      payload.algorithm
    );
    return this.fromResult(result, 'Signature verification completed');
  }

  /**
   * 加密数据
   */
  @MessagePattern('payment.security.encrypt')
  async encryptData(@Payload() payload: EncryptDataPayloadDto): Promise<XResponse<string>> {
    this.logger.log('Encrypt data');

    const result = await this.securityService.encryptSensitiveData(payload.data);
    return this.fromResult(result, 'Data encryption completed');
  }

  /**
   * 解密数据
   */
  @MessagePattern('payment.security.decrypt')
  async decryptData(@Payload() payload: DecryptDataPayloadDto): Promise<XResponse<string>> {
    this.logger.log('Decrypt data');

    const result = await this.securityService.decryptSensitiveData(payload.encryptedData);
    return this.fromResult(result, 'Data decryption completed');
  }

  /**
   * 生成安全令牌
   */
  @MessagePattern('payment.security.generateToken')
  async generateToken(@Payload() payload: GenerateTokenPayloadDto): Promise<XResponse<string>> {
    this.logger.log('Generate security token');

    const result = await this.securityService.generateSecurityToken(
      payload.data,
      payload.expiresIn
    );
    return this.fromResult(result, 'Security token generation completed');
  }

  /**
   * 验证安全令牌
   */
  @MessagePattern('payment.security.validateToken')
  async validateToken(@Payload() payload: ValidateTokenPayloadDto): Promise<XResponse<Record<string, any>>> {
    this.logger.log('Validate security token');

    const result = await this.securityService.validateSecurityToken(payload.token);
    return this.fromResult(result, 'Security token validation completed');
  }
}
