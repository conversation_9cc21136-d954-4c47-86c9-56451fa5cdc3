/**
 * 支付订单Controller
 * 
 * 提供支付订单相关的微服务接口，包括：
 * - 创建支付订单
 * - 查询支付订单
 * - 更新订单状态
 * - 获取订单统计
 * - 订单管理操作
 */

import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ApiTags } from '@nestjs/swagger';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import {XResponse, XResponseUtils, XResultUtils} from '@libs/common/types/result.type';
import { OrderService, CreateOrderResult } from './order.service';
import { PaymentOrderDocument, PaymentOrderStatus, PaymentChannel } from '../../common/schemas/order.schema';
import { PaymentOrderStats } from '../../common/repositories/order.repository';
import {
  CreatePaymentOrderPayload,
  QueryPaymentOrderPayload,
  UpdatePaymentOrderStatusPayload,
  GetOrderDetailPayload,
  GetOrderByTransactionIdPayload,
  GetCharacterOrdersPayload,
  CancelOrderPayload,
  RetryOrderPayload,
} from '../../common/dto/order-payload.dto';

/**
 * 支付订单Controller
 * 
 * 继承BaseController，获得完整的微服务接口处理框架
 */
@ApiTags('payment-order')
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class OrderController extends BaseController {
  constructor(private readonly orderService: OrderService) {
    super(OrderController.name);
  }

  /**
   * 创建支付订单
   */
  @MessagePattern('payment.order.create')
  async createPaymentOrder(@Payload() payload: CreatePaymentOrderPayload): Promise<XResponse<CreateOrderResult>> {
    this.logger.log('Create payment order request');

    // 构建CreatePaymentOrderDto
    const createDto = {
      characterId: payload.characterId,
      userId: payload.userId,
      serverId: payload.serverId,
      rechargeId: payload.rechargeId,
      amount: payload.amount,
      currency: payload.currency,
      channel: payload.channel,
      method: payload.method,
      title: payload.title,
      description: payload.description,
      clientIp: payload.clientIp,
      userAgent: payload.userAgent,
      deviceInfo: payload.deviceInfo,
      metadata: payload.metadata,
    };

    const result = await this.orderService.createPaymentOrder(createDto);
    return this.fromResult(result, 'Payment order created successfully');
  }

  /**
   * 根据第三方交易号获取订单详情
   */
  @MessagePattern('payment.order.getByTransactionId')
  async getOrderByTransactionId(@Payload() payload: GetOrderByTransactionIdPayload): Promise<XResponse<PaymentOrderDocument | null>> {
    this.logger.log(`Get order by transaction ID - transactionId: ${payload.transactionId}`);

    const result = await this.orderService.getOrderByTransactionId(payload.transactionId);
    return this.fromResult(result, 'Get order detail successful');
  }

  /**
   * 查询订单列表
   */
  @MessagePattern('payment.order.query')
  async queryOrders(@Payload() payload: QueryPaymentOrderPayload): Promise<XResponse<{
    orders: PaymentOrderDocument[];
    total: number;
    page: number;
    limit: number;
  }>> {
    this.logger.log('Query orders list');

    // 构建QueryPaymentOrderDto
    const queryDto = {
      orderNo: payload.orderNo,
      characterId: payload.characterId,
      userId: payload.userId,
      serverId: payload.serverId,
      channel: payload.channel,
      status: payload.status,
      startTime: payload.startTime,
      endTime: payload.endTime,
      page: payload.page,
      limit: payload.limit,
    };

    const result = await this.orderService.queryOrders(queryDto);
    return this.fromResult(result, 'Query orders list successful');
  }

  /**
   * 更新订单状态
   */
  @MessagePattern('payment.order.updateStatus')
  async updateOrderStatus(@Payload() payload: UpdatePaymentOrderStatusPayload): Promise<XResponse<PaymentOrderDocument | null>> {
    this.logger.log(`Update order status - orderNo: ${payload.orderNo}`);

    // 构建UpdatePaymentOrderStatusDto
    const updateDto = {
      orderNo: payload.orderNo,
      status: payload.status,
      reason: payload.reason,
      operator: payload.operator,
      metadata: payload.metadata,
    };

    const result = await this.orderService.updateOrderStatus(updateDto);
    return this.fromResult(result, 'Order status updated successfully');
  }

  /**
   * 获取角色的订单列表
   */
  @MessagePattern('payment.order.getCharacterOrders')
  async getCharacterOrders(@Payload() payload: GetCharacterOrdersPayload): Promise<XResponse<PaymentOrderDocument[]>> {
    this.logger.log(`Get character orders - characterId: ${payload.characterId}`);

    const result = await this.orderService.getCharacterOrders(
      payload.characterId,
      payload.limit,
      payload.status
    );
    return this.fromResult(result, 'Get character orders successful');
  }

  /**
   * 取消订单
   */
  @MessagePattern('payment.order.cancel')
  async cancelOrder(@Payload() payload: CancelOrderPayload): Promise<XResponse<PaymentOrderDocument | null>> {
    this.logger.log(`Cancel order - orderNo: ${payload.orderNo}`);

    const result = await this.orderService.updateOrderStatus({
      orderNo: payload.orderNo,
      status: PaymentOrderStatus.CANCELLED,
      reason: payload.reason || 'Order cancelled by user',
      operator: payload.operator,
    });

    return this.fromResult(result, 'Order cancellation successful');
  }

  /**
   * 重试订单
   */
  @MessagePattern('payment.order.retry')
  async retryOrder(@Payload() payload: RetryOrderPayload): Promise<XResponse<PaymentOrderDocument | null>> {
    this.logger.log(`Retry order - orderNo: ${payload.orderNo}`);
    
    // 首先检查订单是否可以重试
    const orderResult = await this.orderService.getOrderByOrderNo(payload.orderNo);
    if (this.fromResult(orderResult).success === false) {
      return this.fromResult(orderResult);
    }

    const order = orderResult.data;
    if (!order) {
      return XResponseUtils.error('ORDER_NOT_FOUND', '订单不存在');
    }

    if (order.status === PaymentOrderStatus.SUCCESS) {
      return XResponseUtils.error('ORDER_ALREADY_SUCCESS', '订单已支付成功，无需重试');
    }

    if (order.retryCount >= 3) {
      return XResponseUtils.error('ORDER_RETRY_LIMIT_EXCEEDED', 'Order retry limit exceeded');
    }

    // 更新订单状态为待支付
    const result = await this.orderService.updateOrderStatus({
      orderNo: payload.orderNo,
      status: PaymentOrderStatus.PENDING,
      reason: payload.reason,
      operator: payload.operator,
      metadata: { retryCount: order.retryCount + 1 },
    });
    
    return this.fromResult(result, 'Order retry successful');
  }

  /**
   * 获取订单详细信息（包含状态历史）
   */
  @MessagePattern('payment.order.getDetail')
  async getOrderDetail(@Payload() payload: GetOrderDetailPayload): Promise<XResponse<PaymentOrderDocument | null>> {
    this.logger.log(`Get order detail - orderNo: ${payload.orderNo}`);

    const result = await this.orderService.getOrderByOrderNo(payload.orderNo);
    return this.fromResult(result, 'Get order detail successful');
  }
}
