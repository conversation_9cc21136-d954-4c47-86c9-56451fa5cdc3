/**
 * 支付订单Service
 * 
 * 负责支付订单的业务逻辑处理，包括：
 * - 订单创建和验证
 * - 订单状态管理
 * - 订单查询和统计
 * - 订单过期处理
 * - 重复订单检查
 */

import {Injectable, Logger} from '@nestjs/common';
import {ConfigService} from '@nestjs/config';
import {Cron, CronExpression} from '@nestjs/schedule';
import {BaseService} from '@libs/common/service/base-service';
import {ServiceResultHandler, XResult, XResultUtils} from '@libs/common/types/result.type';
import {OrderRepository, PaymentOrderStats} from '../../common/repositories/order.repository';
import {PaymentOrder, PaymentOrderDocument, PaymentOrderStatus} from '../../common/schemas/order.schema';
import {CreatePaymentOrderDto, QueryPaymentOrderDto, UpdatePaymentOrderStatusDto} from '../../common/dto/order.dto';
import {AppConfig} from '../../config/app.config';
import {AuditLogService, DataMaskingService} from '../../common/services/audit';
import {AuditEventType, AuditLevel} from '../../common/schemas/audit-log.schema';

/**
 * 订单创建结果接口
 */
export type CreateOrderResult = {
  /** 订单信息 */
  order: PaymentOrderDocument;
  /** 是否为重复订单 */
  isDuplicate: true;
  /** 重复订单信息 */
  duplicateOrder: PaymentOrderDocument;
} | {
  /** 订单信息 */
  order: PaymentOrderDocument;
  /** 是否为重复订单 */
  isDuplicate: false;
  /** 重复订单信息 */
  duplicateOrder?: undefined;
};

/**
 * 支付订单Service
 * 
 * 继承BaseService，获得完整的业务操作框架和Result模式支持
 */
@Injectable()
export class OrderService extends BaseService {
  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly configService: ConfigService,
    private readonly auditLogService: AuditLogService,
    private readonly dataMaskingService: DataMaskingService,
  ) {
    super(OrderService.name);
  }

  /**
   * 创建支付订单
   */
  async createPaymentOrder(createDto: CreatePaymentOrderDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`创建支付订单 - 角色: ${createDto.characterId}, 充值ID: ${createDto.rechargeId}`);

      // 1. 检查重复订单
      const duplicateCheckResult = await this.checkDuplicateOrder(
        createDto.characterId,
        createDto.rechargeId
      );
      if (XResultUtils.isFailure(duplicateCheckResult)) {
        return XResultUtils.error(`检查重复订单失败: ${duplicateCheckResult.message}`, duplicateCheckResult.code);
      }

      const duplicateOrder = duplicateCheckResult.data;
      if (duplicateOrder) {
        this.logger.warn(`发现重复订单 - 订单号: ${duplicateOrder.orderNo}`);
        return XResultUtils.ok({
          order: duplicateOrder,
          isDuplicate: true,
          duplicateOrder,
        });
      }

      // 2. 生成订单号
      const orderNo = this.generateOrderNo();

      // 3. 计算订单过期时间
      const paymentConfig = this.configService.get<AppConfig>('payment');
      const expiresAt = new Date(Date.now() + (paymentConfig?.order.statusTimeoutMinutes || 30) * 60 * 1000);

      // 4. 构建订单数据
      const orderData: Partial<PaymentOrder> = {
        orderNo,
        characterId: createDto.characterId,
        userId: createDto.userId,
        serverId: createDto.serverId,
        rechargeId: createDto.rechargeId,
        amount: createDto.amount,
        currency: createDto.currency || 'CNY',
        channel: createDto.channel,
        method: createDto.method,
        status: PaymentOrderStatus.CREATED,
        title: createDto.title,
        description: createDto.description,
        clientIp: createDto.clientIp,
        userAgent: createDto.userAgent,
        deviceInfo: createDto.deviceInfo,
        expiresAt,
        statusHistory: [{
          status: PaymentOrderStatus.CREATED,
          reason: '订单创建',
          timestamp: new Date(),
        }],
        metadata: createDto.metadata,
      };

      // 5. 保存订单
      const createResult = await this.orderRepository.createOne(orderData);
      if (XResultUtils.isFailure(createResult)) {
        return XResultUtils.error(`保存订单失败: ${createResult.message}`, createResult.code);
      }

      const order = createResult.data;
      this.logger.log(`支付订单创建成功 - 订单号: ${orderNo}`);

      // 记录审计日志
      await this.recordAuditLog({
        eventType: AuditEventType.ORDER_CREATED,
        level: AuditLevel.INFO,
        description: '创建支付订单',
        message: `订单创建成功 - 订单号: ${orderNo}`,
        context: {
          userId: createDto.userId,
          characterId: createDto.characterId,
          orderNo: orderNo,
        },
        requestData: await this.dataMaskingService.maskSensitiveData(createDto),
        responseData: { orderNo, status: order.status },
        success: true,
      });

      return XResultUtils.ok({
        order,
        isDuplicate: false,
        duplicateOrder: undefined,
      });
    }, {
      reason: 'create_payment_order',
      metadata: { characterId: createDto.characterId, rechargeId: createDto.rechargeId }
    });
  }

  /**
   * 根据订单号获取订单
   */
  async getOrderByOrderNo(orderNo: string): Promise<XResult<PaymentOrderDocument | null>> {
    this.logger.debug(`查询支付订单 - 订单号: ${orderNo}`);
    return await this.orderRepository.findByOrderNo(orderNo);
  }

  /**
   * 根据第三方交易号获取订单
   */
  async getOrderByTransactionId(transactionId: string): Promise<XResult<PaymentOrderDocument | null>> {
    this.logger.debug(`查询支付订单 - 交易号: ${transactionId}`);
    return await this.orderRepository.findByTransactionId(transactionId);
  }

  /**
   * 查询订单列表
   */
  async queryOrders(queryDto: QueryPaymentOrderDto): Promise<XResult<{
    orders: PaymentOrderDocument[];
    total: number;
    page: number;
    limit: number;
  }>> {
    this.logger.debug('查询支付订单列表', queryDto);
    return await this.orderRepository.queryOrders(queryDto);
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(updateDto: UpdatePaymentOrderStatusDto): Promise<XResult<PaymentOrderDocument | null>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`更新订单状态 - 订单号: ${updateDto.orderNo}, 状态: ${updateDto.status}`);

      const result = await this.orderRepository.updateOrderStatus(
        updateDto.orderNo,
        updateDto.status,
        updateDto.reason,
        updateDto.operator,
        updateDto.metadata
      );
      if (XResultUtils.isFailure(result)) {
        return XResultUtils.error(`更新订单状态失败: ${result.message}`, result.code);
      }

      const order = result.data;
      if (!order) {
        return XResultUtils.error('订单不存在', 'ORDER_NOT_FOUND');
      }

      this.logger.log(`订单状态更新成功 - 订单号: ${updateDto.orderNo}`);

      // 记录审计日志
      await this.recordAuditLog({
        eventType: AuditEventType.ORDER_STATUS_CHANGED,
        level: AuditLevel.INFO,
        description: '更新订单状态',
        message: `订单状态更新: ${updateDto.orderNo} -> ${updateDto.status}`,
        context: {
          orderNo: updateDto.orderNo,
        },
        requestData: await this.dataMaskingService.maskSensitiveData(updateDto),
        responseData: { orderNo: updateDto.orderNo, status: updateDto.status },
        success: true,
      });

      return XResultUtils.ok(order);
    }, {
      reason: 'update_order_status',
      metadata: { orderNo: updateDto.orderNo, status: updateDto.status }
    });
  }

  /**
   * 获取角色的订单列表
   */
  async getCharacterOrders(
    characterId: string,
    limit: number = 10,
    status?: PaymentOrderStatus
  ): Promise<XResult<PaymentOrderDocument[]>> {
    this.logger.debug(`查询角色订单 - 角色ID: ${characterId}`);
    return await this.orderRepository.findByCharacterId(characterId, limit, status);
  }

  /**
   * 获取支付统计数据
   */
  async getPaymentStats(
    startTime?: Date,
    endTime?: Date,
    serverId?: string
  ): Promise<XResult<PaymentOrderStats>> {
    this.logger.debug('获取支付统计数据');
    return await this.orderRepository.getPaymentStats(startTime, endTime, serverId);
  }

  /**
   * 检查重复订单
   */
  private async checkDuplicateOrder(
    characterId: string,
    rechargeId: number
  ): Promise<XResult<PaymentOrderDocument | null>> {
    const paymentConfig = this.configService.get<AppConfig>('payment');
    const windowMinutes = paymentConfig?.security.duplicateCheckWindowMinutes || 5;

    return await this.orderRepository.checkDuplicateOrder(
      characterId,
      rechargeId,
      windowMinutes
    );
  }

  /**
   * 生成订单号
   */
  private generateOrderNo(): string {
    const paymentConfig = this.configService.get<AppConfig>('payment');
    const prefix = paymentConfig?.order.orderIdPrefix || 'PAY';
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * 定时处理过期订单
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleExpiredOrders(): Promise<void> {
    try {
      this.logger.debug('开始处理过期订单');

      const expiredOrdersResult = await this.orderRepository.findExpiredOrders(100);
      if (XResultUtils.isFailure(expiredOrdersResult)) {
        this.logger.error('查询过期订单失败', expiredOrdersResult.message);
        return;
      }

      const expiredOrders = expiredOrdersResult.data;
      if (expiredOrders.length === 0) {
        return;
      }

      this.logger.log(`发现 ${expiredOrders.length} 个过期订单，开始处理`);

      for (const order of expiredOrders) {
        try {
          await this.orderRepository.updateOrderStatus(
            order.orderNo,
            PaymentOrderStatus.EXPIRED,
            '订单超时自动过期',
            'system'
          );
          
          this.logger.debug(`订单已过期 - 订单号: ${order.orderNo}`);
        } catch (error) {
          this.logger.error(`处理过期订单失败 - 订单号: ${order.orderNo}`, error);
        }
      }

      this.logger.log(`过期订单处理完成，共处理 ${expiredOrders.length} 个订单`);
    } catch (error) {
      this.logger.error('处理过期订单异常', error);
    }
  }

  /**
   * 增加回调次数
   */
  async incrementCallbackCount(orderNo: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      const result = await this.orderRepository.incrementCallbackCount(orderNo);
      if (XResultUtils.isFailure(result)) {
        return XResultUtils.error(`增加回调次数失败: ${result.message}`, 'INCREMENT_CALLBACK_COUNT_FAILED');
      }
      return XResultUtils.ok(true);
    }, {
      reason: 'increment_callback_count',
      metadata: { orderNo }
    });
  }

  /**
   * 标记订单已通知
   */
  async markAsNotified(orderNo: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      const result = await this.orderRepository.markAsNotified(orderNo);
      if (XResultUtils.isFailure(result)) {
        return XResultUtils.error(`标记订单已通知失败: ${result.message}`, 'MARK_AS_NOTIFIED_FAILED');
      }
      return XResultUtils.ok(true);
    }, {
      reason: 'mark_as_notified',
      metadata: { orderNo }
    });
  }

  /**
   * 记录审计日志的辅助方法
   */
  private async recordAuditLog(auditData: {
    eventType: AuditEventType;
    level: AuditLevel;
    description: string;
    message?: string;
    context: any;
    requestData?: any;
    responseData?: any;
    success?: boolean;
    errorMessage?: string;
  }): Promise<void> {
    try {
      await this.auditLogService.createAuditLog({
        eventType: auditData.eventType,
        level: auditData.level,
        description: auditData.description,
        message: auditData.message,
        context: auditData.context,
        requestData: auditData.requestData,
        responseData: auditData.responseData,
        success: auditData.success !== false,
        errorMessage: auditData.errorMessage,
        riskLevel: 'LOW',
        metadata: {
          service: 'OrderService',
          timestamp: new Date(),
        },
      });
    } catch (error) {
      // 审计日志记录失败不应该影响业务流程
      this.logger.warn('记录审计日志失败', error);
    }
  }
}
