/**
 * 支付回调Service
 * 
 * 负责处理第三方支付平台的回调通知，包括：
 * - 回调验证和安全检查
 * - 订单状态更新
 * - 业务系统通知
 * - 回调重试和异常处理
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseService } from '@libs/common/service/base-service';
import { XResult, XResultUtils, ServiceResultHandler } from '@libs/common/types/result.type';
import { OrderService } from '../order/order.service';
import { GatewayService } from '../gateway/gateway.service';
import { PaymentOrderStatus, PaymentChannel } from '../../common/schemas/order.schema';
import { AppConfig } from '../../config/app.config';
import { AuditLogService, DataMaskingService } from '../../common/services/audit';
import { SignatureService } from '../../common/services/security';
import { AuditEventType, AuditLevel } from '../../common/schemas/audit-log.schema';

/**
 * 回调处理结果接口
 */
export interface CallbackProcessResult {
  /** 是否处理成功 */
  success: boolean;
  /** 订单号 */
  orderNo: string;
  /** 处理前状态 */
  previousStatus: PaymentOrderStatus;
  /** 处理后状态 */
  currentStatus: PaymentOrderStatus;
  /** 是否需要通知业务系统 */
  needNotify: boolean;
  /** 处理消息 */
  message: string;
  /** 额外数据 */
  metadata?: Record<string, any>;
}

/**
 * 支付回调Service
 */
@Injectable()
export class CallbackService extends BaseService {
  constructor(
    private readonly orderService: OrderService,
    private readonly gatewayService: GatewayService,
    private readonly configService: ConfigService,
    private readonly auditLogService: AuditLogService,
    private readonly dataMaskingService: DataMaskingService,
    private readonly signatureService: SignatureService,
  ) {
    super(CallbackService.name);
  }

  /**
   * 处理支付回调
   */
  async processCallback(
    channel: PaymentChannel,
    callbackData: Record<string, any>
  ): Promise<XResult<CallbackProcessResult>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`处理支付回调 - 渠道: ${channel}`);

      // 1. 验证回调签名
      const verifyResult = await this.gatewayService.verifyCallback(channel, callbackData);
      if (XResultUtils.isFailure(verifyResult)) {
        return XResultUtils.error(`验证回调签名失败: ${verifyResult.message}`, verifyResult.code);
      }

      if (!verifyResult.data) {
        return XResultUtils.error('回调签名验证失败', 'CALLBACK_SIGNATURE_INVALID');
      }

      // 2. 提取订单信息
      const orderInfo = this.extractOrderInfo(channel, callbackData);
      if (!orderInfo.orderNo) {
        return XResultUtils.error('无法从回调数据中提取订单号', 'ORDER_NO_MISSING');
      }

      // 3. 查询订单
      const orderResult = await this.orderService.getOrderByOrderNo(orderInfo.orderNo);
      if (XResultUtils.isFailure(orderResult)) {
        return XResultUtils.error(`查询订单失败: ${orderResult.message}`, orderResult.code);
      }

      const order = orderResult.data;
      if (!order) {
        return XResultUtils.error('订单不存在', 'ORDER_NOT_FOUND');
      }

      // 4. 检查订单状态
      const previousStatus = order.status;
      if (order.status === PaymentOrderStatus.SUCCESS) {
        // 订单已经成功，返回成功但不需要处理
        return XResultUtils.ok({
          success: true,
          orderNo: order.orderNo,
          previousStatus,
          currentStatus: order.status,
          needNotify: false,
          message: '订单已处理，无需重复处理',
        });
      }

      // 5. 更新回调次数
      await this.orderService.incrementCallbackCount(order.orderNo);

      // 6. 根据回调结果更新订单状态
      const newStatus = this.mapCallbackStatusToOrderStatus(orderInfo.paymentStatus);
      const updateResult = await this.orderService.updateOrderStatus({
        orderNo: order.orderNo,
        status: newStatus,
        reason: `支付回调更新 - ${orderInfo.paymentStatus}`,
        operator: 'system',
        metadata: {
          channel,
          transactionId: orderInfo.transactionId,
          paidAmount: orderInfo.amount,
          paidAt: orderInfo.paidAt,
          callbackData: this.sanitizeCallbackData(callbackData),
        },
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新订单状态失败: ${updateResult.message}`, updateResult.code);
      }

      // 7. 构建处理结果
      const result: CallbackProcessResult = {
        success: true,
        orderNo: order.orderNo,
        previousStatus,
        currentStatus: newStatus,
        needNotify: newStatus === PaymentOrderStatus.SUCCESS,
        message: `订单状态已更新: ${previousStatus} -> ${newStatus}`,
        metadata: {
          transactionId: orderInfo.transactionId,
          amount: orderInfo.amount,
          paidAt: orderInfo.paidAt,
        },
      };

      // 8. 记录审计日志
      await this.recordAuditLog({
        eventType: AuditEventType.PAYMENT_CALLBACK_PROCESSED,
        level: AuditLevel.INFO,
        description: '处理支付回调',
        message: `回调处理成功 - 订单号: ${order.orderNo}, 状态: ${previousStatus} -> ${newStatus}`,
        context: {
          orderNo: order.orderNo,
          channel,
          transactionId: orderInfo.transactionId,
        },
        requestData: await this.dataMaskingService.maskSensitiveData(callbackData),
        responseData: { success: true, status: newStatus },
        success: true,
      });

      this.logger.log(`支付回调处理完成 - 订单号: ${order.orderNo}, 状态: ${newStatus}`);
      return XResultUtils.ok(result);
    }, {
      reason: 'process_payment_callback',
      metadata: { channel }
    });
  }

  /**
   * 通知业务系统
   */
  async notifyBusinessSystem(
    orderNo: string,
    notifyType: 'success' | 'failed' = 'success'
  ): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`通知业务系统 - 订单号: ${orderNo}, 类型: ${notifyType}`);

      // 1. 获取订单信息
      const orderResult = await this.orderService.getOrderByOrderNo(orderNo);
      if (XResultUtils.isFailure(orderResult)) {
        return XResultUtils.error(`查询订单失败: ${orderResult.message}`, orderResult.code);
      }

      const order = orderResult.data;
      if (!order) {
        return XResultUtils.error('订单不存在', 'ORDER_NOT_FOUND');
      }

      // 2. 构建通知数据
      const notifyData = {
        orderNo: order.orderNo,
        characterId: order.characterId,
        userId: order.userId,
        serverId: order.serverId,
        rechargeId: order.rechargeId,
        amount: order.amount,
        currency: order.currency,
        status: order.status,
        paidAt: order.paidAt,
        channel: order.channel,
        transactionId: order.thirdPartyInfo?.transactionId,
      };

      // 3. 获取通知配置
      const paymentConfig = this.configService.get<AppConfig>('payment');
      const targetService = notifyType === 'success' 
        ? paymentConfig?.notification.successNotifyService 
        : paymentConfig?.notification.failureNotifyService;

      if (!targetService) {
        this.logger.warn(`未配置${notifyType}通知服务`);
        return XResultUtils.ok(false);
      }

      // 4. 调用业务系统接口
      const notifyResult = await this.callMicroservice(
        targetService,
        'payment.notify',
        notifyData,
        {
          timeout: paymentConfig?.notification.notifyTimeoutMs || 5000,
          retries: paymentConfig?.notification.notifyRetryCount || 3,
        }
      );

      if (XResultUtils.isFailure(notifyResult)) {
        this.logger.error(`通知业务系统失败 - 订单号: ${orderNo}`, notifyResult.message);
        return XResultUtils.ok(false);
      }

      // 5. 标记订单已通知
      await this.orderService.markAsNotified(orderNo);

      this.logger.log(`业务系统通知成功 - 订单号: ${orderNo}`);
      return XResultUtils.ok(true);
    }, {
      reason: 'notify_business_system',
      metadata: { orderNo, notifyType }
    });
  }

  /**
   * 从回调数据中提取订单信息
   */
  private extractOrderInfo(channel: PaymentChannel, callbackData: Record<string, any>): {
    orderNo: string;
    transactionId: string;
    paymentStatus: string;
    amount: number;
    paidAt: Date;
  } {
    let orderNo = '';
    let transactionId = '';
    let paymentStatus = '';
    let amount = 0;
    let paidAt = new Date();

    switch (channel) {
      case PaymentChannel.ALIPAY:
        orderNo = callbackData.out_trade_no || '';
        transactionId = callbackData.trade_no || '';
        paymentStatus = callbackData.trade_status || '';
        amount = callbackData.total_amount ? Math.round(parseFloat(callbackData.total_amount) * 100) : 0;
        paidAt = callbackData.gmt_payment ? new Date(callbackData.gmt_payment) : new Date();
        break;

      case PaymentChannel.WECHAT:
        orderNo = callbackData.out_trade_no || '';
        transactionId = callbackData.transaction_id || '';
        paymentStatus = callbackData.trade_state || '';
        amount = callbackData.amount?.total || 0;
        paidAt = callbackData.success_time ? new Date(callbackData.success_time) : new Date();
        break;

      case PaymentChannel.APPLE_PAY:
        orderNo = callbackData.orderNo || '';
        transactionId = callbackData.transaction_id || '';
        paymentStatus = 'SUCCESS'; // 苹果支付回调通常表示成功
        amount = callbackData.amount || 0;
        paidAt = callbackData.purchase_date ? new Date(callbackData.purchase_date) : new Date();
        break;

      default:
        this.logger.warn(`未知的支付渠道: ${channel}`);
        break;
    }

    return { orderNo, transactionId, paymentStatus, amount, paidAt };
  }

  /**
   * 映射回调状态到订单状态
   */
  private mapCallbackStatusToOrderStatus(paymentStatus: string): PaymentOrderStatus {
    // 支付宝状态映射
    if (['TRADE_SUCCESS', 'TRADE_FINISHED'].includes(paymentStatus)) {
      return PaymentOrderStatus.SUCCESS;
    }
    if (['TRADE_CLOSED'].includes(paymentStatus)) {
      return PaymentOrderStatus.CANCELLED;
    }
    if (['WAIT_BUYER_PAY'].includes(paymentStatus)) {
      return PaymentOrderStatus.PENDING;
    }

    // 微信支付状态映射
    if (paymentStatus === 'SUCCESS') {
      return PaymentOrderStatus.SUCCESS;
    }
    if (['REFUND', 'CLOSED', 'REVOKED'].includes(paymentStatus)) {
      return PaymentOrderStatus.CANCELLED;
    }
    if (['NOTPAY', 'USERPAYING'].includes(paymentStatus)) {
      return PaymentOrderStatus.PENDING;
    }

    // 默认为失败
    return PaymentOrderStatus.FAILED;
  }

  /**
   * 清理回调数据中的敏感信息
   */
  private sanitizeCallbackData(callbackData: Record<string, any>): Record<string, any> {
    const sanitized = { ...callbackData };

    // 移除敏感字段
    const sensitiveFields = ['sign', 'signature', 'password', 'key', 'secret'];
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '***';
      }
    });

    return sanitized;
  }

  /**
   * 记录审计日志的辅助方法
   */
  private async recordAuditLog(auditData: {
    eventType: AuditEventType;
    level: AuditLevel;
    description: string;
    message?: string;
    context: any;
    requestData?: any;
    responseData?: any;
    success?: boolean;
    errorMessage?: string;
  }): Promise<void> {
    try {
      await this.auditLogService.createAuditLog({
        eventType: auditData.eventType,
        level: auditData.level,
        description: auditData.description,
        message: auditData.message,
        context: auditData.context,
        requestData: auditData.requestData,
        responseData: auditData.responseData,
        success: auditData.success !== false,
        errorMessage: auditData.errorMessage,
        riskLevel: 'LOW',
        metadata: {
          service: 'CallbackService',
          timestamp: new Date(),
        },
      });
    } catch (error) {
      // 审计日志记录失败不应该影响业务流程
      this.logger.warn('记录审计日志失败', error);
    }
  }
}
