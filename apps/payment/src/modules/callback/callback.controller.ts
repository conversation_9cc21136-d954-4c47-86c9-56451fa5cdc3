/**
 * 支付回调Controller
 * 
 * 提供支付回调相关的接口，包括：
 * - HTTP回调接口（支付宝、微信、苹果）
 * - 微服务回调处理接口
 * - 回调状态查询接口
 * - 业务系统通知接口
 */

import { Controller, Post, Get, Body, Param, Headers, Ip, Logger, UsePipes, HttpCode, HttpStatus } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import { XResponse, XResponseUtils } from '@libs/common/types/result.type';
import { CallbackService, CallbackProcessResult } from './callback.service';
import { PaymentChannel } from '../../common/schemas/order.schema';
import {
  ProcessCallbackPayloadDto,
  NotifyBusinessSystemPayloadDto,
} from '../../common/dto/callback-payload.dto';

/**
 * 支付回调Controller
 */
@ApiTags('payment-callback')
@Controller('callback')
@UsePipes(StandardMicroserviceValidationPipe)
export class CallbackController extends BaseController {
  constructor(private readonly callbackService: CallbackService) {
    super(CallbackController.name);
  }

  // ========== HTTP回调接口（供第三方支付平台调用） ==========

  /**
   * 支付宝支付回调
   */
  @Post('alipay')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '支付宝支付回调' })
  @ApiResponse({ status: 200, description: '回调处理成功', type: String })
  @ApiBody({ description: '支付宝回调数据' })
  async alipayCallback(
    @Body() callbackData: Record<string, any>,
    @Ip() clientIp: string,
    @Headers() headers: Record<string, string>
  ): Promise<string> {
    this.logger.log(`收到支付宝回调 - IP: ${clientIp}`);

    try {
      // 处理支付宝回调
      const result = await this.callbackService.processCallback(
        PaymentChannel.ALIPAY,
        { ...callbackData, clientIp, headers }
      );

      if (this.fromResult(result).success && result.data.success) {
        // 支付宝要求返回"success"字符串
        this.logger.log(`支付宝回调处理成功 - 订单号: ${result.data.orderNo}`);
        
        // 如果需要通知业务系统
        if (result.data.needNotify) {
          this.callbackService.notifyBusinessSystem(result.data.orderNo, 'success')
            .catch(error => this.logger.error('通知业务系统失败', error));
        }
        
        return 'success';
      } else {
        this.logger.error('支付宝回调处理失败', result);
        return 'fail';
      }
    } catch (error) {
      this.logger.error('支付宝回调异常', error);
      return 'fail';
    }
  }

  /**
   * 微信支付回调
   */
  @Post('wechat')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '微信支付回调' })
  @ApiResponse({ status: 200, description: '回调处理成功' })
  @ApiBody({ description: '微信支付回调数据' })
  async wechatCallback(
    @Body() callbackData: Record<string, any>,
    @Ip() clientIp: string,
    @Headers() headers: Record<string, string>
  ): Promise<{ code: string; message: string }> {
    this.logger.log(`收到微信支付回调 - IP: ${clientIp}`);

    try {
      // 处理微信支付回调
      const result = await this.callbackService.processCallback(
        PaymentChannel.WECHAT,
        { 
          ...callbackData, 
          clientIp, 
          headers,
          // 微信支付V3的回调头部信息
          'Wechatpay-Timestamp': headers['wechatpay-timestamp'],
          'Wechatpay-Nonce': headers['wechatpay-nonce'],
          'Wechatpay-Signature': headers['wechatpay-signature'],
          'Wechatpay-Serial': headers['wechatpay-serial'],
        }
      );

      if (this.fromResult(result).success && result.data.success) {
        this.logger.log(`微信支付回调处理成功 - 订单号: ${result.data.orderNo}`);
        
        // 如果需要通知业务系统
        if (result.data.needNotify) {
          this.callbackService.notifyBusinessSystem(result.data.orderNo, 'success')
            .catch(error => this.logger.error('通知业务系统失败', error));
        }
        
        // 微信支付要求返回JSON格式
        return { code: 'SUCCESS', message: '成功' };
      } else {
        this.logger.error('微信支付回调处理失败', result);
        return { code: 'FAIL', message: '失败' };
      }
    } catch (error) {
      this.logger.error('微信支付回调异常', error);
      return { code: 'FAIL', message: '异常' };
    }
  }

  /**
   * 苹果支付回调（实际上是收据验证）
   */
  @Post('apple')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '苹果支付收据验证' })
  @ApiResponse({ status: 200, description: '收据验证成功' })
  @ApiBody({ description: '苹果支付收据数据' })
  async applePayCallback(
    @Body() callbackData: { receiptData: string; orderNo: string },
    @Ip() clientIp: string
  ): Promise<{ success: boolean; message: string; data?: any }> {
    this.logger.log(`收到苹果支付收据验证请求 - 订单号: ${callbackData.orderNo}, IP: ${clientIp}`);

    try {
      // 处理苹果支付回调
      const result = await this.callbackService.processCallback(
        PaymentChannel.APPLE_PAY,
        { ...callbackData, clientIp }
      );

      if (this.fromResult(result).success && result.data.success) {
        this.logger.log(`苹果支付回调处理成功 - 订单号: ${result.data.orderNo}`);
        
        // 如果需要通知业务系统
        if (result.data.needNotify) {
          this.callbackService.notifyBusinessSystem(result.data.orderNo, 'success')
            .catch(error => this.logger.error('通知业务系统失败', error));
        }
        
        return {
          success: true,
          message: '收据验证成功',
          data: result.data.metadata,
        };
      } else {
        this.logger.error('苹果支付回调处理失败', result);
        return {
          success: false,
          message: this.fromResult(result).message || '收据验证失败',
        };
      }
    } catch (error) {
      this.logger.error('苹果支付回调异常', error);
      return {
        success: false,
        message: '收据验证异常',
      };
    }
  }

  /**
   * 通用回调状态查询
   */
  @Get('status/:orderNo')
  @ApiOperation({ summary: '查询回调处理状态' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async getCallbackStatus(@Param('orderNo') orderNo: string): Promise<{
    success: boolean;
    data?: {
      orderNo: string;
      callbackCount: number;
      lastCallbackAt: Date;
      isNotified: boolean;
      notifiedAt: Date;
    };
    message: string;
  }> {
    this.logger.log(`查询回调状态 - 订单号: ${orderNo}`);

    try {
      // 这里应该查询订单的回调状态
      // 暂时返回模拟数据
      return {
        success: true,
        data: {
          orderNo,
          callbackCount: 1,
          lastCallbackAt: new Date(),
          isNotified: true,
          notifiedAt: new Date(),
        },
        message: '查询成功',
      };
    } catch (error) {
      this.logger.error('查询回调状态异常', error);
      return {
        success: false,
        message: '查询失败',
      };
    }
  }

  // ========== 微服务接口（供内部服务调用） ==========

  /**
   * 处理支付回调（微服务接口）
   */
  @MessagePattern('payment.callback.process')
  async processCallback(@Payload() payload: ProcessCallbackPayloadDto): Promise<XResponse<CallbackProcessResult>> {
    this.logger.log(`Process payment callback - channel: ${payload.channel}`);

    const result = await this.callbackService.processCallback(payload.channel, payload.callbackData);
    return this.fromResult(result, 'Payment callback processing completed');
  }

  /**
   * 通知业务系统（微服务接口）
   */
  @MessagePattern('payment.callback.notify')
  async notifyBusinessSystem(@Payload() payload: NotifyBusinessSystemPayloadDto): Promise<XResponse<boolean>> {
    this.logger.log(`Notify business system - orderNo: ${payload.orderNo}`);

    const result = await this.callbackService.notifyBusinessSystem(
      payload.orderNo,
      payload.notifyType || 'success'
    );
    return this.fromResult(result, 'Business system notification completed');
  }
}
