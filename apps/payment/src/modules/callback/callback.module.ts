/**
 * 支付回调模块
 * 
 * 处理第三方支付平台的回调通知，包括：
 * - 回调接收和验证
 * - 订单状态更新
 * - 业务通知处理
 * - 回调重试机制
 * - 回调日志记录
 */

import { Module } from '@nestjs/common';
import { CallbackService } from './callback.service';
import { CallbackController } from './callback.controller';
import { SECURITY_SERVICES } from '../../common/services/security';
import { AUDIT_SERVICES } from '../../common/services/audit';
import { PaymentAuditLog, AuditLogSchema } from '../../common/schemas/audit-log.schema';
import { AuditLogRepository } from '../../common/repositories/audit-log.repository';
import { OrderModule } from '../order/order.module';
import { GatewayModule } from '../gateway/gateway.module';

/**
 * 支付回调模块
 * 
 * 提供完整的支付回调处理功能：
 * - 多渠道回调统一处理
 * - 回调验证和安全检查
 * - 订单状态同步更新
 * - 业务系统通知
 */
@Module({
  imports: [
    OrderModule,    // 订单管理
    GatewayModule,  // 网关验证
    // 注册MongoDB模型
    MongooseModule.forFeature([
      {
        name: PaymentAuditLog.name,
        schema: AuditLogSchema,
      },
    ]),
  ],

  controllers: [
    CallbackController,
  ],

  providers: [
    CallbackService,
    AuditLogRepository,
    // 注册安全服务
    ...SECURITY_SERVICES,
    // 注册审计服务
    ...AUDIT_SERVICES,
  ],

  exports: [
    CallbackService,
  ],
})
export class CallbackModule {}
