/**
 * 支付网关Controller
 * 
 * 提供统一的支付网关微服务接口，包括：
 * - 创建支付
 * - 查询支付状态
 * - 验证支付回调
 * - 支付渠道管理
 */

import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ApiTags } from '@nestjs/swagger';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import {XResponse, XResponseUtils, XResultUtils} from '@libs/common/types/result.type';
import { GatewayService, PaymentRequest, PaymentResponse, PaymentQueryResponse } from './gateway.service';
import { ApplePayGatewayService } from './services/applepay-gateway.service';
import { PaymentChannel } from '../../common/schemas/order.schema';
import {
  CreatePaymentPayloadDto,
  QueryPaymentPayloadDto,
  VerifyCallbackPayloadDto,
  VerifyAppleReceiptPayloadDto,
} from '../../common/dto/gateway-payload.dto';

/**
 * 支付网关Controller
 */
@ApiTags('payment-gateway')
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class GatewayController extends BaseController {
  constructor(
    private readonly gatewayService: GatewayService,
    private readonly applePayGatewayService: ApplePayGatewayService,
  ) {
    super(GatewayController.name);
  }

  /**
   * 创建支付
   */
  @MessagePattern('payment.gateway.createPayment')
  async createPayment(@Payload() payload: CreatePaymentPayloadDto): Promise<XResponse<PaymentResponse>> {
    this.logger.log(`Create payment - channel: ${payload.channel}, orderNo: ${payload.orderNo}`);

    // 构建PaymentRequest
    const request: PaymentRequest = {
      orderNo: payload.orderNo,
      amount: payload.amount,
      currency: payload.currency || 'CNY',
      title: payload.title,
      description: payload.description,
      notifyUrl: payload.notifyUrl,
      returnUrl: payload.returnUrl,
      metadata: payload.metadata,
    };

    const result = await this.gatewayService.createPayment(payload.channel, request);
    return this.fromResult(result, 'Payment created successfully');
  }

  /**
   * 查询支付状态
   */
  @MessagePattern('payment.gateway.queryPayment')
  async queryPayment(@Payload() payload: QueryPaymentPayloadDto): Promise<XResponse<PaymentQueryResponse>> {
    this.logger.log(`Query payment status - channel: ${payload.channel}, transactionId: ${payload.transactionId}`);

    const result = await this.gatewayService.queryPayment(payload.channel, payload.transactionId);
    return this.fromResult(result, 'Payment status query successful');
  }

  /**
   * 验证支付回调
   */
  @MessagePattern('payment.gateway.verifyCallback')
  async verifyCallback(@Payload() payload: VerifyCallbackPayloadDto): Promise<XResponse<boolean>> {
    this.logger.log(`Verify payment callback - channel: ${payload.channel}`);

    const result = await this.gatewayService.verifyCallback(payload.channel, payload.callbackData);
    return this.fromResult(result, 'Payment callback verification completed');
  }

  /**
   * 验证苹果支付收据
   */
  @MessagePattern('payment.gateway.verifyAppleReceipt')
  async verifyAppleReceipt(@Payload() payload: VerifyAppleReceiptPayloadDto): Promise<XResponse<PaymentQueryResponse>> {
    this.logger.log(`Verify Apple Pay receipt - orderNo: ${payload.orderNo}`);

    const result = await this.applePayGatewayService.verifyReceipt(payload.receiptData, payload.orderNo);
    return this.fromResult(result, 'Apple Pay receipt verification completed');
  }
}
