/**
 * 支付网关模块
 * 
 * 集成第三方支付平台，包括：
 * - 支付宝支付集成
 * - 微信支付集成
 * - 苹果支付集成
 * - 统一支付网关接口
 * - 支付参数构建和签名
 */

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { GatewayService } from './gateway.service';
import { AlipayGatewayService } from './services/alipay-gateway.service';
import { WechatGatewayService } from './services/wechat-gateway.service';
import { ApplePayGatewayService } from './services/applepay-gateway.service';
import { GatewayController } from './gateway.controller';
import { SECURITY_SERVICES } from '../../common/services/security';
import { AUDIT_SERVICES } from '../../common/services/audit';
import { PaymentAuditLog, AuditLogSchema } from '../../common/schemas/audit-log.schema';
import { AuditLogRepository } from '../../common/repositories/audit-log.repository';

/**
 * 支付网关模块
 * 
 * 提供统一的第三方支付平台集成：
 * - 多支付渠道支持
 * - 统一的支付接口
 * - 支付参数标准化
 * - 支付结果处理
 */
@Module({
  controllers: [
    GatewayController,
  ],

  providers: [
    // 主要网关服务
    GatewayService,
    
    // 各支付平台服务
    AlipayGatewayService,
    WechatGatewayService,
    ApplePayGatewayService,
  ],

  exports: [
    GatewayService,
    AlipayGatewayService,
    WechatGatewayService,
    ApplePayGatewayService,
  ],
})
export class GatewayModule {}
