/**
 * 支付审计Controller
 * 
 * 提供支付审计相关的微服务接口，包括：
 * - 审计日志记录
 * - 审计日志查询
 * - 审计统计分析
 * - 合规报告生成
 */

import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ApiTags } from '@nestjs/swagger';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import { XResponse } from '@libs/common/types/result.type';
import { AuditService, AuditRecordRequest } from './audit.service';
import { PaymentAuditLog } from '../../common/schemas/audit-log.schema';
import { RecordAuditPayloadDto } from '../../common/dto/audit-payload.dto';

@ApiTags('payment-audit')
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class AuditController extends BaseController {
  constructor(private readonly paymentAuditService: AuditService) {
    super(AuditController.name);
  }

  /**
   * 记录审计日志
   */
  @MessagePattern('payment.audit.record')
  async recordAudit(@Payload() payload: RecordAuditPayloadDto): Promise<XResponse<PaymentAuditLog>> {
    this.logger.log(`Record audit log - event: ${payload.eventType}`);

    // 构建AuditRecordRequest
    const request: AuditRecordRequest = {
      eventType: payload.eventType,
      level: payload.level,
      description: payload.description,
      message: payload.message,
      context: {
        userId: payload.userId,
        characterId: payload.characterId,
        orderNo: payload.orderNo,
        transactionId: payload.transactionId,
        paymentChannel: payload.paymentChannel,
        clientIp: payload.clientIp,
        userAgent: payload.userAgent,
      },
      requestData: payload.requestData,
      responseData: payload.responseData,
      errorMessage: payload.errorMessage,
      errorStack: payload.errorStack,
      processingTime: payload.processingTime,
      success: payload.success,
      riskLevel: payload.riskLevel,
      metadata: payload.metadata,
    };

    const result = await this.paymentAuditService.recordAudit(request);
    return this.fromResult(result, 'Audit log recording completed');
  }
}
